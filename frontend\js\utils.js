/**
 * 工具函數模組 - 包含陣列洗牌、字體調整、語音播放和練習題目生成功能
 */

/**
 * 陣列洗牌函數 (<PERSON><PERSON><PERSON> 算法)
 * @param {Array} array - 要洗牌的陣列
 * @returns {Array} 洗牌後的新陣列
 */
function shuffleArray(array) {
    const newArray = [...array]; // 創建副本，避免修改原陣列
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

/**
 * 動態字體調整函數 - 確保文字適合容器寬度
 * @param {HTMLElement} element - 要調整字體的元素
 * @param {number} minFontSize - 最小字體大小 (預設: 10px)
 */
function adjustFontSize(element, minFontSize = 10) {
    if (!element) return;

    // 重置字體大小到預設值
    element.style.fontSize = '';
    const initialFontSize = parseFloat(window.getComputedStyle(element).fontSize);
    let currentFontSize = initialFontSize;

    // 檢查是否溢出並逐步減小字體
    while (element.scrollWidth > element.clientWidth && currentFontSize > minFontSize) {
        currentFontSize -= 1;
        element.style.fontSize = `${currentFontSize}px`;
    }

    console.log(`字體調整: ${initialFontSize}px -> ${currentFontSize}px`);
}

/**
 * 防抖動函數 - 限制函數執行頻率
 * @param {Function} func - 要防抖動的函數
 * @param {number} wait - 等待時間 (毫秒)
 * @returns {Function} 防抖動後的函數
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 語音播放函數
 * @param {string} text - 要播放的文字
 * @param {string} lang - 語言代碼 (預設: 'en-US')
 * @param {number} rate - 語速 (預設: 0.8)
 */
function speakWord(text, lang = 'en-US', rate = 0.8) {
    if (!text) {
        console.warn('沒有提供要播放的文字');
        return;
    }

    if (!('speechSynthesis' in window)) {
        console.warn('瀏覽器不支援語音播放功能');
        showMessage('您的瀏覽器不支援語音播放功能', 'warning');
        return;
    }

    try {
        // 停止當前播放
        window.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = lang;
        utterance.rate = rate;
        utterance.volume = 1.0;

        utterance.onstart = function() {
            console.log('開始播放:', text);
        };

        utterance.onend = function() {
            console.log('播放完成:', text);
        };

        utterance.onerror = function(event) {
            console.error('語音播放錯誤:', event.error);
            showMessage('語音播放失敗', 'error');
        };

        window.speechSynthesis.speak(utterance);
    } catch (error) {
        console.error('語音播放錯誤:', error);
        showMessage('語音播放失敗', 'error');
    }
}

/**
 * 生成練習題目
 * @param {Array} vocabularies - 詞彙列表
 * @param {Array} allVocabularies - 所有詞彙列表 (用於生成錯誤選項)
 * @returns {Array} 練習題目列表
 */
function generatePracticeQuestions(vocabularies, allVocabularies) {
    if (!vocabularies || vocabularies.length === 0) {
        return [];
    }

    const questions = [];
    const allWords = allVocabularies || vocabularies;

    vocabularies.forEach(vocab => {
        // 1. 多選題 - 根據英文定義選擇正確單詞
        const wrongOptions = allWords
            .filter(w => w.id !== vocab.id)
            .sort(() => 0.5 - Math.random())
            .slice(0, 3);

        questions.push({
            type: 'multiple-choice',
            instruction: '根據解釋選擇正確的單詞：',
            questionText: `"${vocab.definition_en}"`,
            options: shuffleArray([vocab, ...wrongOptions]),
            correctAnswer: vocab.text,
            vocabulary: vocab
        });

        // 2. 拼寫練習 - 根據英文定義拼寫單詞
        questions.push({
            type: 'typing',
            instruction: '根據解釋拼寫出正確的單詞：',
            questionText: `"${vocab.definition_en}"`,
            correctAnswer: vocab.text,
            vocabulary: vocab
        });

        // 3. 填空題 - 在例句中填入正確單詞
        const sentenceWithBlank = vocab.sentence_en.replace(
            new RegExp(vocab.text.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi'), 
            '______'
        );

        questions.push({
            type: 'fill-in-the-blank',
            instruction: '在空格中填入正確的單詞：',
            questionText: sentenceWithBlank,
            correctAnswer: vocab.text,
            vocabulary: vocab
        });
    });

    return shuffleArray(questions);
}

/**
 * 生成 Emoji SVG 圖片
 * @param {string} emoji - Emoji 字符
 * @returns {string} SVG 數據 URL
 */
function generateEmojiSVG(emoji) {
    return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E${emoji}%3C/text%3E%3C/svg%3E`;
}

/**
 * 單詞到 Emoji 的映射
 */
const imageMapping = {
    'festival': '🎉',
    'hobby': '🎨',
    'delicious': '🍔',
    'adventure': '🏔️',
    'friendship': '🤝',
    'technology': '💻',
    'environment': '🌳',
    'communicate': '💬',
    'opportunity': '🚪',
    'challenge': '🎯',
    'government': '🏛️',
    'responsibility': '📋',
    'global': '🌍',
    'democracy': '🗳️',
    'sustainable': '♻️'
};

/**
 * 根據單詞獲取對應的 Emoji
 * @param {string} word - 單詞
 * @returns {string} Emoji 字符
 */
function getEmojiForWord(word) {
    return imageMapping[word.toLowerCase()] || '📚';
}

/**
 * 顯示訊息提示
 * @param {string} message - 訊息內容
 * @param {string} type - 訊息類型 ('success', 'error', 'warning', 'info')
 * @param {number} duration - 顯示時間 (毫秒，預設: 3000)
 */
function showMessage(message, type = 'info', duration = 3000) {
    const messageDiv = document.getElementById('error-message');
    const messageText = document.getElementById('error-text');
    
    if (!messageDiv || !messageText) return;

    // 設置訊息內容
    messageText.textContent = message;
    
    // 設置樣式
    messageDiv.className = `fixed top-4 left-4 right-4 px-4 py-3 rounded z-50`;
    
    switch (type) {
        case 'success':
            messageDiv.className += ' bg-green-100 border border-green-400 text-green-700';
            break;
        case 'error':
            messageDiv.className += ' bg-red-100 border border-red-400 text-red-700';
            break;
        case 'warning':
            messageDiv.className += ' bg-yellow-100 border border-yellow-400 text-yellow-700';
            break;
        default:
            messageDiv.className += ' bg-blue-100 border border-blue-400 text-blue-700';
    }
    
    // 顯示訊息
    messageDiv.classList.remove('hidden');
    
    // 自動隱藏
    setTimeout(() => {
        messageDiv.classList.add('hidden');
    }, duration);
}

/**
 * 顯示載入動畫
 * @param {boolean} show - 是否顯示
 */
function showLoading(show = true) {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        if (show) {
            loadingScreen.classList.remove('hidden');
        } else {
            loadingScreen.classList.add('hidden');
        }
    }
}

/**
 * 檢查答案是否正確 (忽略大小寫和前後空格)
 * @param {string} userAnswer - 用戶答案
 * @param {string} correctAnswer - 正確答案
 * @returns {boolean} 是否正確
 */
function checkAnswer(userAnswer, correctAnswer) {
    if (!userAnswer || !correctAnswer) return false;
    
    const normalizedUser = userAnswer.trim().toLowerCase();
    const normalizedCorrect = correctAnswer.trim().toLowerCase();
    
    return normalizedUser === normalizedCorrect;
}

/**
 * 格式化百分比
 * @param {number} score - 分數
 * @param {number} total - 總分
 * @returns {number} 百分比
 */
function calculatePercentage(score, total) {
    if (total === 0) return 0;
    return Math.round((score / total) * 100);
}

// 防抖動的字體調整函數
const debouncedAdjustFont = debounce(adjustFontSize, 100);
