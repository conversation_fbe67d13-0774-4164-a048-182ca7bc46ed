/**
 * 數據服務模組 - 處理詞彙數據載入、年級篩選和本地存儲功能
 */
class DataService {
    static vocabularyData = null;
    static isLoaded = false;

    /**
     * 載入詞彙數據從 JSON 文件
     * @returns {Promise<Object>} 詞彙數據
     */
    static async loadVocabularies() {
        if (this.isLoaded && this.vocabularyData) {
            return this.vocabularyData;
        }

        try {
            console.log('正在載入詞彙數據...');
            const response = await fetch('data/mockData.json');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.vocabularyData = data;
            this.isLoaded = true;
            
            console.log('詞彙數據載入成功:', data.vocabularies.length, '個詞彙');
            return data;
        } catch (error) {
            console.error('載入詞彙數據失敗:', error);
            throw new Error('無法載入詞彙數據，請檢查網路連接或重新整理頁面');
        }
    }

    /**
     * 根據年級篩選詞彙
     * @param {string} grade - 年級 (S1, S2, S3)
     * @returns {Array} 該年級的詞彙列表
     */
    static getVocabulariesByGrade(grade) {
        if (!this.vocabularyData || !this.vocabularyData.vocabularies) {
            console.warn('詞彙數據尚未載入');
            return [];
        }

        const vocabularies = this.vocabularyData.vocabularies.filter(vocab => {
            // 從 word_id 提取年級，例如 "s1_001" -> "S1"
            const vocabGrade = vocab.word_id.substring(0, 2).toUpperCase();
            return vocabGrade === grade.toUpperCase();
        });

        console.log(`找到 ${vocabularies.length} 個 ${grade} 年級詞彙`);
        return vocabularies;
    }

    /**
     * 獲取所有詞彙（用於生成練習選項）
     * @returns {Array} 所有詞彙列表
     */
    static getAllVocabularies() {
        if (!this.vocabularyData || !this.vocabularyData.vocabularies) {
            return [];
        }
        return this.vocabularyData.vocabularies;
    }

    /**
     * 保存學習進度到 localStorage
     * @param {Object} progress - 進度對象
     */
    static saveProgress(progress) {
        try {
            const progressData = {
                currentGrade: progress.currentGrade,
                currentWordIndex: progress.currentWordIndex,
                completedWords: progress.completedWords || [],
                lastUpdated: new Date().toISOString()
            };
            
            localStorage.setItem('vocab_learning_progress', JSON.stringify(progressData));
            console.log('學習進度已保存');
        } catch (error) {
            console.error('保存學習進度失敗:', error);
        }
    }

    /**
     * 從 localStorage 載入學習進度
     * @returns {Object|null} 進度對象或 null
     */
    static loadProgress() {
        try {
            const saved = localStorage.getItem('vocab_learning_progress');
            if (saved) {
                const progress = JSON.parse(saved);
                console.log('學習進度已載入:', progress);
                return progress;
            }
            return null;
        } catch (error) {
            console.error('載入學習進度失敗:', error);
            return null;
        }
    }

    /**
     * 保存練習成績
     * @param {string} grade - 年級
     * @param {number} score - 成績百分比
     */
    static savePracticeScore(grade, score) {
        try {
            const scores = this.getPracticeScores();
            scores[grade] = Math.max(scores[grade] || 0, score); // 保存最高分
            scores.lastUpdated = new Date().toISOString();
            
            localStorage.setItem('vocab_practice_scores', JSON.stringify(scores));
            console.log(`${grade} 年級練習成績已保存: ${score}%`);
        } catch (error) {
            console.error('保存練習成績失敗:', error);
        }
    }

    /**
     * 獲取所有練習成績
     * @returns {Object} 成績對象
     */
    static getPracticeScores() {
        try {
            const saved = localStorage.getItem('vocab_practice_scores');
            if (saved) {
                return JSON.parse(saved);
            }
            return { S1: 0, S2: 0, S3: 0 };
        } catch (error) {
            console.error('載入練習成績失敗:', error);
            return { S1: 0, S2: 0, S3: 0 };
        }
    }

    /**
     * 獲取特定年級的最高成績
     * @param {string} grade - 年級
     * @returns {number} 成績百分比
     */
    static getGradeScore(grade) {
        const scores = this.getPracticeScores();
        return scores[grade] || 0;
    }

    /**
     * 清除所有本地數據
     */
    static clearAllData() {
        try {
            localStorage.removeItem('vocab_learning_progress');
            localStorage.removeItem('vocab_practice_scores');
            console.log('所有本地數據已清除');
        } catch (error) {
            console.error('清除數據失敗:', error);
        }
    }

    /**
     * 獲取數據統計信息
     * @returns {Object} 統計信息
     */
    static getStatistics() {
        if (!this.vocabularyData) {
            return { total: 0, byGrade: {} };
        }

        const stats = {
            total: this.vocabularyData.vocabularies.length,
            byGrade: {}
        };

        // 按年級統計
        ['S1', 'S2', 'S3'].forEach(grade => {
            stats.byGrade[grade] = this.getVocabulariesByGrade(grade).length;
        });

        return stats;
    }

    /**
     * 驗證詞彙數據完整性
     * @returns {Object} 驗證結果
     */
    static validateData() {
        if (!this.vocabularyData || !this.vocabularyData.vocabularies) {
            return { valid: false, errors: ['數據未載入'] };
        }

        const errors = [];
        const vocabularies = this.vocabularyData.vocabularies;

        vocabularies.forEach((vocab, index) => {
            if (!vocab.text) errors.push(`詞彙 ${index + 1}: 缺少單詞文本`);
            if (!vocab.phonetic) errors.push(`詞彙 ${index + 1}: 缺少音標`);
            if (!vocab.definition_en) errors.push(`詞彙 ${index + 1}: 缺少英文定義`);
            if (!vocab.translation_zh_tw) errors.push(`詞彙 ${index + 1}: 缺少中文翻譯`);
            if (!vocab.sentence_en) errors.push(`詞彙 ${index + 1}: 缺少例句`);
            if (!vocab.grade) errors.push(`詞彙 ${index + 1}: 缺少年級信息`);
        });

        return {
            valid: errors.length === 0,
            errors: errors,
            totalVocabularies: vocabularies.length
        };
    }
}
