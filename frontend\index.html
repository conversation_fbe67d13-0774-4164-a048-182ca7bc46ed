<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生字卡記憶工具 - MVP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-slate-50 text-slate-800 flex flex-col p-4">
    <div id="app-container" class="w-full max-w-md md:max-w-4xl mx-auto flex flex-col flex-grow justify-center h-full">

        <!-- 起始畫面 -->
        <div id="start-screen" class="text-center">
            <!-- Logo 右上角 -->
            <div class="fixed top-4 right-4 z-50">
                <img src="logo.png" alt="Logo" class="h-12 md:h-16 lg:h-20 w-auto">
            </div>
            <h1 class="text-3xl sm:text-4xl font-bold mb-2 text-slate-900">生字卡記憶工具</h1>
            <p class="text-slate-600 mb-8">專為香港初中學生設計</p>
            <div class="space-y-4">
                <button onclick="startLearning('S1')" class="w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    中一 (S1) 詞彙
                </button>
                <button onclick="startLearning('S2')" class="w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    中二 (S2) 詞彙
                </button>
                <button onclick="startLearning('S3')" class="w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    中三 (S3) 詞彙
                </button>
            </div>
        </div>

        <!-- 學習畫面 -->
        <div id="study-screen" class="hidden flex flex-col flex-grow h-full">
            <!-- 頂部導航 -->
            <div class="flex-shrink-0 flex justify-between items-center mb-4">
                <button onclick="showStartScreen()" class="text-slate-500 hover:text-slate-800">&larr; 返回主頁</button>
                <div class="flex items-center">
                    <img src="logo.png" alt="Logo" class="h-8 md:h-12 lg:h-14 w-auto">
                    <span id="grade-display" class="hidden bg-blue-100 text-blue-800 px-2 py-1 rounded ml-2"></span>
                </div>
            </div>

            <!-- 詞彙卡片 -->
            <div class="flex-grow flex flex-col min-h-0 justify-center">
                <div id="flashcard" class="w-full bg-white rounded-xl shadow-lg border border-slate-200 p-4 sm:p-6 flex flex-col md:flex-row min-h-0 mb-4">
                    <!-- 左側區域：單詞+語音+圖片 (平板以上) -->
                    <div class="flex flex-col md:w-1/2 md:pr-6">
                        <!-- 單詞和語音按鈕區域 -->
                        <div class="flex-shrink-0 relative">
                            <div class="flex items-start justify-between md:block">
                                <div class="flex-grow min-w-0 mr-2 md:mr-12 md:pr-0">
                                    <h2 id="card-word" class="word-display text-3xl sm:text-4xl font-bold text-slate-900 break-all whitespace-nowrap"></h2>
                                    <p id="card-phonetic" class="phonetic-display text-base sm:text-lg text-slate-500 mt-1 break-words"></p>
                                </div>
                                <button onclick="speakCurrentWord()" class="flex-shrink-0 p-2 rounded-full hover:bg-slate-100 active:bg-slate-200 transition-colors md:absolute md:top-0 md:right-0">
                                    <svg class="w-6 h-6 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 圖片區域 -->
                        <div id="card-image-container" class="w-full image-container-responsive flex-shrink-0 flex items-center justify-center mt-3 md:mt-4">
                        </div>
                    </div>

                    <!-- 右側區域：文字內容 (平板以上) / 下方區域 (手機) -->
                    <div class="flex flex-col justify-center md:justify-center h-full mt-4 md:mt-0 md:w-1/2 md:pl-6 text-sm overflow-y-auto subtle-scrollbar">
                            <div class="space-y-2">
                                <div>
                                    <p class="font-semibold text-slate-900">中文解釋:</p>
                                    <p id="card-translation-zh" class="text-slate-700"></p>
                                </div>
                                <div>
                                    <p class="font-semibold text-slate-900">英文解釋:</p>
                                    <p id="card-definition-en" class="text-slate-700"></p>
                                </div>
                                <div>
                                    <p class="font-semibold text-slate-900">例句:</p>
                                    <p id="card-sentence-en" class="text-slate-700"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 控制按鈕 -->
                <div class="flex-shrink-0 max-w-md mx-auto w-full">
                    <div class="flex items-center gap-3">
                        <button id="prev-btn" onclick="previousCard()" class="flex-1 bg-white border border-slate-300 hover:bg-slate-100 text-slate-800 font-semibold py-3 rounded-lg shadow-sm">&larr; 上一個</button>
                        <div class="flex-shrink-0">
                            <span id="progress-display" class="bg-blue-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-sm"></span>
                        </div>
                        <button id="next-btn" onclick="nextCard()" class="flex-1 bg-white border border-slate-300 hover:bg-slate-100 text-slate-800 font-semibold py-3 rounded-lg shadow-sm">下一個 &rarr;</button>
                    </div>
                    <button onclick="startPractice()" class="mt-4 w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 rounded-lg shadow-sm">開始練習！</button>
                </div>
            </div>
        </div>

        <!-- 練習畫面 -->
        <div id="practice-screen" class="hidden flex flex-col justify-center min-h-screen md:justify-center md:items-center">
            <!-- Logo 右上角 -->
            <div class="fixed top-4 right-4 z-50">
                <img src="logo.png" alt="Logo" class="h-8 md:h-12 lg:h-14 w-auto">
            </div>
            <div class="w-full max-w-md mx-auto px-4">
                <p id="practice-instruction" class="text-center text-slate-600 mb-2 font-semibold"></p>
                <div id="practice-question" class="text-center text-slate-800 mb-4 text-lg"></div>
                <div id="practice-content" class="space-y-3">
                    <!-- 練習內容將由 JavaScript 動態生成 -->
                </div>
            </div>
        </div>

        <!-- 結果畫面 -->
        <div id="results-screen" class="hidden text-center">
            <!-- Logo 右上角 -->
            <div class="fixed top-4 right-4 z-50">
                <img src="logo.png" alt="Logo" class="h-8 md:h-12 lg:h-14 w-auto">
            </div>
            <h2 class="text-2xl font-bold mb-4">練習完成！</h2>
            <p class="text-lg mb-2">你的成績是:</p>
            <p id="score-display" class="text-5xl font-bold text-blue-600 mb-8"></p>
            <button onclick="backToStudy()" class="w-full bg-slate-800 hover:bg-slate-900 text-white font-bold py-3 rounded-lg shadow-sm">再次學習</button>
        </div>
    </div>

    <!-- 載入動畫 -->
    <div id="loading-screen" class="hidden fixed inset-0 bg-slate-50 flex items-center justify-center z-50">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-slate-600">載入中...</p>
        </div>
    </div>

    <!-- 錯誤提示 -->
    <div id="error-message" class="hidden fixed top-4 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
        <span id="error-text"></span>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/dataService.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
