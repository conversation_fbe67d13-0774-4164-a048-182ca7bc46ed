# 英語詞彙學習工具 - MVP 前端開發規格文檔

## 📋 專案概述

### 專案名稱
English Vocabulary Learning Tool - MVP Frontend

### 專案目標
開發一個簡化的前端 MVP，專注於詞彙學習核心功能，不包含任何登入系統或用戶管理功能。

### 核心特色
- 基於 JSON 數據源的詞彙學習系統
- 響應式設計（手機優先）
- 三種練習模式
- 本地進度保存
- 語音播放功能

## 🏗️ 技術架構

### 技術棧
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: TailwindCSS (CDN)
- **字體**: Google Fonts (Inter)
- **數據源**: JSON 文件
- **存儲**: localStorage
- **語音**: Web Speech API

### 文件結構
```
frontend/
├── index.html                    # 主入口頁面
├── css/
│   └── styles.css               # 自定義樣式
├── js/
│   ├── app.js                   # 主應用邏輯
│   ├── dataService.js           # JSON 數據處理服務
│   └── utils.js                 # 工具函數
├── data/
│   └── mockData.json           # 詞彙數據
└── images/
    └── placeholder/             # 佔位圖片
```

## 📊 數據結構設計

### JSON 數據格式 (mockData.json)
```json
{
  "vocabularies": [
    {
      "id": 1,
      "word_id": "s1_001",
      "text": "festival",
      "phonetic": "/ˈfes.tɪ.vəl/",
      "part_of_speech": "noun",
      "definition_en": "A special day or period...",
      "translation_zh_tw": "節日",
      "sentence_en": "Chinese New Year is...",
      "image_path": "images/festival.jpg",
      "grade": "S1"
    }
  ]
}
```

### localStorage 數據結構
```javascript
// 學習進度
localStorage.setItem('learning_progress', JSON.stringify({
  currentGrade: 'S1',
  currentWordIndex: 0,
  completedWords: [1, 2, 3],
  practiceScores: {
    'S1': 85,
    'S2': 0,
    'S3': 0
  }
}));
```

## 🎯 功能需求詳細說明

### 1. 主頁面 (index.html)

#### 1.1 起始畫面
- **標題**: "生字卡記憶工具"
- **副標題**: "專為香港初中學生設計"
- **年級選擇按鈕**:
  - 中一 (S1) 詞彙
  - 中二 (S2) 詞彙  
  - 中三 (S3) 詞彙
- **按鈕樣式**: 白色背景，hover 效果，點擊縮放動畫

#### 1.2 學習模式界面
**頁面布局**:
- **頂部導航**: 返回按鈕 + 年級顯示 + 進度顯示
- **詞彙卡片**: 主要內容區域
- **底部控制**: 上一個/下一個 + 開始練習按鈕

**詞彙卡片內容**:
- **單詞**: 大字體顯示，支援動態字體調整
- **音標**: 中等字體，灰色
- **語音按鈕**: 右上角，點擊播放發音
- **圖片**: emoji SVG 格式，響應式大小
- **中文解釋**: 標籤 + 內容
- **英文解釋**: 標籤 + 內容  
- **例句**: 標籤 + 內容

#### 1.3 練習模式界面
**三種練習類型**:

1. **多選題**:
   - 顯示英文定義
   - 4個選項按鈕
   - 點擊後顯示正確/錯誤反饋

2. **拼寫練習**:
   - 顯示英文定義
   - 文字輸入框
   - 確定按鈕
   - 支援 Enter 鍵提交

3. **填空題**:
   - 顯示例句，單詞用 "______" 替代
   - 文字輸入框
   - 確定按鈕

#### 1.4 結果頁面
- **標題**: "練習完成！"
- **成績顯示**: 大字體百分比
- **再次學習按鈕**: 返回學習模式

## 🎨 響應式設計規範

### 設計原則
基於 Reference/index.html 的響應式設計，保持以下特點：

#### 全局設置
```css
html, body {
    height: 100%;
    overflow: hidden;
}

body {
    font-family: 'Inter', sans-serif;
    height: 100dvh; /* 手機高度適配 */
}
```

#### 容器布局
```css
.app-container {
    width: 100%;
    max-width: 28rem; /* 手機優先 */
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    height: 100%;
}
```

#### 響應式斷點
- **手機**: < 640px (預設)
- **平板**: >= 640px (sm:)
- **桌面**: >= 1024px (lg:)

#### 詞彙卡片響應式
```css
/* 手機版: 垂直布局 */
.card-content {
    flex-direction: column;
}

/* 桌面版: 水平布局 */
@media (min-width: 640px) {
    .card-content {
        flex-direction: row;
    }
    
    .image-container {
        width: 25%;
    }
    
    .text-container {
        width: 75%;
        padding-left: 1rem;
    }
}
```

## 🔧 JavaScript 功能實現

### 主要類別和函數

#### 1. 應用狀態管理
```javascript
const AppState = {
    currentMode: 'start', // 'start', 'study', 'practice', 'results'
    currentGrade: null,   // 'S1', 'S2', 'S3'
    vocabularies: [],     // 當前年級的詞彙列表
    currentWordIndex: 0,  // 當前詞彙索引
    practiceQuestions: [], // 練習題目
    currentQuestionIndex: 0, // 當前題目索引
    score: 0             // 練習分數
};
```

#### 2. 數據服務 (dataService.js)
```javascript
class DataService {
    static async loadVocabularies() {
        // 從 mockData.json 載入詞彙數據
    }
    
    static getVocabulariesByGrade(grade) {
        // 根據年級篩選詞彙
    }
    
    static saveProgress(progress) {
        // 保存學習進度到 localStorage
    }
    
    static loadProgress() {
        // 從 localStorage 載入學習進度
    }
}
```

#### 3. 工具函數 (utils.js)
```javascript
// 陣列洗牌
function shuffleArray(array) { }

// 動態字體調整
function adjustFontSize(element) { }

// 語音播放
function speakWord(text) { }

// 生成練習題目
function generatePracticeQuestions(vocabularies) { }
```

#### 4. 主應用邏輯 (app.js)
```javascript
// 初始化應用
function initApp() { }

// 開始學習
function startLearning(grade) { }

// 顯示詞彙卡片
function displayVocabularyCard() { }

// 開始練習
function startPractice() { }

// 檢查答案
function checkAnswer(userAnswer, correctAnswer) { }

// 顯示結果
function showResults() { }
```

## 🎯 具體實現要求

### 1. 字體調整機制
```javascript
function adjustFontSize(element) {
    element.style.fontSize = '';
    const initialFontSize = parseFloat(window.getComputedStyle(element).fontSize);
    let currentFontSize = initialFontSize;
    
    while (element.scrollWidth > element.clientWidth && currentFontSize > 10) {
        currentFontSize -= 1;
        element.style.fontSize = `${currentFontSize}px`;
    }
}
```

### 2. 語音播放功能
```javascript
function speakWord(text) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'en-US';
        window.speechSynthesis.speak(utterance);
    }
}
```

### 3. 練習題目生成
- **多選題**: 1個正確答案 + 3個錯誤選項
- **拼寫題**: 顯示定義，要求輸入單詞
- **填空題**: 例句中單詞替換為空格

### 4. 進度保存機制
- 當前學習位置
- 各年級練習成績
- 已完成的詞彙列表

## 🔐 圖片處理方案

### Emoji SVG 格式
```javascript
function generateEmojiSVG(emoji) {
    return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E${emoji}%3C/text%3E%3C/svg%3E`;
}
```

### 圖片映射
```javascript
const imageMapping = {
    'festival': '🎉',
    'hobby': '🎨', 
    'delicious': '🍔',
    'technology': '💻',
    'environment': '🌳',
    'government': '🏛️'
};
```

### 未來擴展支援
- 保留 `image_path` 欄位
- 支援 JPG/PNG 格式
- 圖片載入錯誤處理

## 📱 用戶體驗要求

### 觸控優化
- 按鈕最小點擊區域 44px
- hover 效果在觸控設備上禁用
- 點擊反饋動畫

### 載入體驗
- 數據載入時顯示載入動畫
- 圖片載入失敗顯示佔位符
- 平滑的頁面切換動畫

### 無障礙設計
- 適當的 ARIA 標籤
- 鍵盤導航支援
- 高對比度文字

## 🚀 開發階段

### 階段 1: 基礎架構
- [ ] 創建文件結構
- [ ] 設置 HTML 模板
- [ ] 引入 TailwindCSS 和字體
- [ ] 創建基本 CSS 樣式

### 階段 2: 數據和狀態管理
- [ ] 實現 DataService
- [ ] 設置 AppState
- [ ] 實現 localStorage 功能

### 階段 3: 學習功能
- [ ] 起始畫面
- [ ] 詞彙卡片顯示
- [ ] 導航功能
- [ ] 語音播放

### 階段 4: 練習功能
- [ ] 練習題目生成
- [ ] 三種練習模式
- [ ] 答案檢查和反饋
- [ ] 結果頁面

### 階段 5: 優化和測試
- [ ] 響應式測試
- [ ] 性能優化
- [ ] 無障礙測試
- [ ] 跨瀏覽器測試

## 📊 成功指標

### 功能指標
- [ ] 支援三個年級的詞彙學習
- [ ] 三種練習模式正常運作
- [ ] 進度保存功能正常
- [ ] 語音播放功能正常

### 性能指標
- [ ] 頁面載入時間 < 2秒
- [ ] 動畫流暢度 60fps
- [ ] 響應式設計在所有設備正常

### 用戶體驗指標
- [ ] 5分鐘內完成一輪學習
- [ ] 直觀的操作界面
- [ ] 清晰的視覺反饋

## 💻 詳細實現指南

### HTML 結構模板

#### index.html 基本結構
```html
<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生字卡記憶工具 - MVP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-slate-50 text-slate-800 flex flex-col p-4">
    <div id="app-container" class="w-full max-w-md mx-auto flex flex-col flex-grow justify-center h-full">
        <!-- 起始畫面 -->
        <div id="start-screen" class="text-center">
            <!-- 內容 -->
        </div>

        <!-- 學習畫面 -->
        <div id="study-screen" class="hidden flex flex-col flex-grow h-full">
            <!-- 內容 -->
        </div>

        <!-- 練習畫面 -->
        <div id="practice-screen" class="hidden flex flex-col flex-grow justify-center">
            <!-- 內容 -->
        </div>

        <!-- 結果畫面 -->
        <div id="results-screen" class="hidden text-center">
            <!-- 內容 -->
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/dataService.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
```

### CSS 樣式實現

#### 關鍵 CSS 類別
```css
/* 基於 Reference/index.html 的樣式 */
.flashcard {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

@media (min-width: 640px) {
    .flashcard {
        padding: 1.5rem;
    }
}

.word-display {
    font-size: 3rem;
    font-weight: bold;
    color: #0f172a;
    word-break: break-all;
    white-space: nowrap;
}

@media (min-width: 640px) {
    .word-display {
        font-size: 4rem;
    }
}

.phonetic-display {
    font-size: 1rem;
    color: #64748b;
    margin-top: 0.25rem;
    word-break: break-words;
}

@media (min-width: 640px) {
    .phonetic-display {
        font-size: 1.125rem;
    }
}
```

### JavaScript 實現細節

#### 1. 應用初始化流程
```javascript
// app.js
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // 1. 載入數據
        await DataService.loadVocabularies();

        // 2. 載入進度
        const progress = DataService.loadProgress();
        if (progress) {
            AppState.loadFromProgress(progress);
        }

        // 3. 初始化 UI
        initializeUI();

        // 4. 綁定事件
        bindEvents();

        console.log('應用初始化完成');
    } catch (error) {
        console.error('應用初始化失敗:', error);
        showErrorMessage('載入失敗，請重新整理頁面');
    }
});
```

#### 2. 狀態管理實現
```javascript
const AppState = {
    currentMode: 'start',
    currentGrade: null,
    vocabularies: [],
    currentWordIndex: 0,
    practiceQuestions: [],
    currentQuestionIndex: 0,
    score: 0,

    // 切換模式
    switchMode(newMode) {
        this.currentMode = newMode;
        updateUI();
    },

    // 從進度載入狀態
    loadFromProgress(progress) {
        this.currentGrade = progress.currentGrade;
        this.currentWordIndex = progress.currentWordIndex;
    },

    // 保存當前狀態
    saveProgress() {
        const progress = {
            currentGrade: this.currentGrade,
            currentWordIndex: this.currentWordIndex,
            practiceScores: DataService.getPracticeScores()
        };
        DataService.saveProgress(progress);
    }
};
```

#### 3. 數據服務實現
```javascript
// dataService.js
class DataService {
    static vocabularyData = null;

    static async loadVocabularies() {
        try {
            const response = await fetch('data/mockData.json');
            const data = await response.json();
            this.vocabularyData = data;
            return data;
        } catch (error) {
            console.error('載入詞彙數據失敗:', error);
            throw error;
        }
    }

    static getVocabulariesByGrade(grade) {
        if (!this.vocabularyData) return [];

        return this.vocabularyData.vocabularies.filter(vocab => {
            // 從 word_id 提取年級，例如 "s1_001" -> "S1"
            const vocabGrade = vocab.word_id.substring(0, 2).toUpperCase();
            return vocabGrade === grade;
        });
    }

    static saveProgress(progress) {
        try {
            localStorage.setItem('vocab_learning_progress', JSON.stringify(progress));
        } catch (error) {
            console.error('保存進度失敗:', error);
        }
    }

    static loadProgress() {
        try {
            const saved = localStorage.getItem('vocab_learning_progress');
            return saved ? JSON.parse(saved) : null;
        } catch (error) {
            console.error('載入進度失敗:', error);
            return null;
        }
    }

    static savePracticeScore(grade, score) {
        try {
            const scores = this.getPracticeScores();
            scores[grade] = score;
            localStorage.setItem('vocab_practice_scores', JSON.stringify(scores));
        } catch (error) {
            console.error('保存練習成績失敗:', error);
        }
    }

    static getPracticeScores() {
        try {
            const saved = localStorage.getItem('vocab_practice_scores');
            return saved ? JSON.parse(saved) : { S1: 0, S2: 0, S3: 0 };
        } catch (error) {
            console.error('載入練習成績失敗:', error);
            return { S1: 0, S2: 0, S3: 0 };
        }
    }
}
```

### 錯誤處理和邊界情況

#### 1. 數據載入失敗
```javascript
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'fixed top-4 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
    errorDiv.textContent = message;
    document.body.appendChild(errorDiv);

    setTimeout(() => {
        errorDiv.remove();
    }, 5000);
}
```

#### 2. 語音播放失敗處理
```javascript
function speakWord(text) {
    if (!('speechSynthesis' in window)) {
        console.warn('瀏覽器不支援語音播放');
        return;
    }

    try {
        // 停止當前播放
        window.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'en-US';
        utterance.rate = 0.8; // 稍慢的語速

        utterance.onerror = function(event) {
            console.error('語音播放失敗:', event.error);
        };

        window.speechSynthesis.speak(utterance);
    } catch (error) {
        console.error('語音播放錯誤:', error);
    }
}
```

#### 3. 圖片載入失敗處理
```javascript
function setVocabularyImage(container, vocabulary) {
    const img = document.createElement('img');
    img.className = 'w-full h-full object-contain';
    img.alt = vocabulary.text;

    // 優先使用真實圖片
    if (vocabulary.image_path && vocabulary.image_path !== '') {
        img.src = vocabulary.image_path;
        img.onerror = function() {
            // 載入失敗時使用 emoji
            img.src = generateEmojiSVG(getEmojiForWord(vocabulary.text));
        };
    } else {
        // 直接使用 emoji
        img.src = generateEmojiSVG(getEmojiForWord(vocabulary.text));
    }

    container.innerHTML = '';
    container.appendChild(img);
}
```

### 性能優化建議

#### 1. 圖片預載入
```javascript
function preloadImages(vocabularies) {
    vocabularies.forEach(vocab => {
        if (vocab.image_path) {
            const img = new Image();
            img.src = vocab.image_path;
        }
    });
}
```

#### 2. 防抖動處理
```javascript
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 使用防抖動處理字體調整
const debouncedAdjustFont = debounce(adjustFontSize, 100);
```

### 測試檢查清單

#### 功能測試
- [ ] 年級選擇功能正常
- [ ] 詞彙卡片顯示完整
- [ ] 上一個/下一個導航正常
- [ ] 語音播放功能正常
- [ ] 三種練習模式正常
- [ ] 答案檢查正確
- [ ] 成績計算正確
- [ ] 進度保存和載入正常

#### 響應式測試
- [ ] iPhone SE (375px) 顯示正常
- [ ] iPad (768px) 顯示正常
- [ ] Desktop (1024px+) 顯示正常
- [ ] 橫屏模式顯示正常
- [ ] 字體大小自動調整正常

#### 瀏覽器兼容性
- [ ] Chrome (最新版)
- [ ] Safari (最新版)
- [ ] Firefox (最新版)
- [ ] Edge (最新版)

#### 性能測試
- [ ] 首次載入時間 < 2秒
- [ ] 頁面切換流暢
- [ ] 記憶體使用合理
- [ ] 沒有 JavaScript 錯誤

---

**文檔版本**: 1.0.0
**最後更新**: 2025-01-31
**維護者**: 開發團隊
