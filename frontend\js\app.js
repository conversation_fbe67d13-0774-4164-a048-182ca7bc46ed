/**
 * 主應用邏輯 - 英語詞彙學習工具 MVP
 */

// 應用狀態管理
const AppState = {
    currentMode: 'start', // 'start', 'study', 'practice', 'results'
    currentGrade: null,   // 'S1', 'S2', 'S3'
    vocabularies: [],     // 當前年級的詞彙列表
    allVocabularies: [],  // 所有詞彙列表 (用於生成練習選項)
    currentWordIndex: 0,  // 當前詞彙索引
    practiceQuestions: [], // 練習題目
    currentQuestionIndex: 0, // 當前題目索引
    score: 0,             // 練習分數
    
    // 切換模式
    switchMode(newMode) {
        this.currentMode = newMode;
        updateUI();
    },
    
    // 從進度載入狀態
    loadFromProgress(progress) {
        if (progress) {
            this.currentGrade = progress.currentGrade;
            this.currentWordIndex = progress.currentWordIndex || 0;
        }
    },
    
    // 保存當前狀態
    saveProgress() {
        const progress = {
            currentGrade: this.currentGrade,
            currentWordIndex: this.currentWordIndex,
            completedWords: [], // 可以擴展為追蹤已完成的詞彙
        };
        DataService.saveProgress(progress);
    }
};

// DOM 元素引用
let domElements = {};

/**
 * 初始化 DOM 元素引用
 */
function initializeDOMElements() {
    domElements = {
        startScreen: document.getElementById('start-screen'),
        studyScreen: document.getElementById('study-screen'),
        practiceScreen: document.getElementById('practice-screen'),
        resultsScreen: document.getElementById('results-screen'),
        gradeDisplay: document.getElementById('grade-display'),
        progressDisplay: document.getElementById('progress-display'),
        cardWord: document.getElementById('card-word'),
        cardPhonetic: document.getElementById('card-phonetic'),
        cardImageContainer: document.getElementById('card-image-container'),
        cardTranslationZh: document.getElementById('card-translation-zh'),
        cardDefinitionEn: document.getElementById('card-definition-en'),
        cardSentenceEn: document.getElementById('card-sentence-en'),
        prevBtn: document.getElementById('prev-btn'),
        nextBtn: document.getElementById('next-btn'),
        practiceInstruction: document.getElementById('practice-instruction'),
        practiceQuestion: document.getElementById('practice-question'),
        practiceContent: document.getElementById('practice-content'),
        scoreDisplay: document.getElementById('score-display')
    };
}

/**
 * 應用初始化
 */
async function initApp() {
    try {
        console.log('正在初始化應用...');
        showLoading(true);
        
        // 初始化 DOM 元素
        initializeDOMElements();
        
        // 載入詞彙數據
        await DataService.loadVocabularies();
        AppState.allVocabularies = DataService.getAllVocabularies();
        
        // 載入學習進度
        const progress = DataService.loadProgress();
        AppState.loadFromProgress(progress);
        
        // 初始化 UI
        updateUI();
        
        console.log('應用初始化完成');
        showMessage('應用載入完成！', 'success', 2000);
        
    } catch (error) {
        console.error('應用初始化失敗:', error);
        showMessage(error.message || '應用載入失敗，請重新整理頁面', 'error', 5000);
    } finally {
        showLoading(false);
    }
}

/**
 * 更新 UI 顯示
 */
function updateUI() {
    // 隱藏所有主要畫面
    const screens = [
        domElements.startScreen,
        domElements.studyScreen,
        domElements.practiceScreen,
        domElements.resultsScreen
    ];

    screens.forEach(screen => {
        if (screen && screen.classList) {
            screen.classList.add('hidden');
        }
    });

    // 顯示對應畫面
    switch (AppState.currentMode) {
        case 'start':
            domElements.startScreen?.classList.remove('hidden');
            break;
        case 'study':
            domElements.studyScreen?.classList.remove('hidden');
            displayVocabularyCard();
            break;
        case 'practice':
            domElements.practiceScreen?.classList.remove('hidden');
            displayPracticeQuestion();
            break;
        case 'results':
            domElements.resultsScreen?.classList.remove('hidden');
            displayResults();
            break;
    }
}

/**
 * 開始學習特定年級
 * @param {string} grade - 年級 (S1, S2, S3)
 */
function startLearning(grade) {
    try {
        console.log(`開始學習 ${grade} 年級詞彙`);
        
        AppState.currentGrade = grade;
        AppState.vocabularies = shuffleArray(DataService.getVocabulariesByGrade(grade));
        AppState.currentWordIndex = 0;
        AppState.score = 0;
        
        if (AppState.vocabularies.length === 0) {
            showMessage(`沒有找到 ${grade} 年級的詞彙數據`, 'warning');
            return;
        }
        
        AppState.switchMode('study');
        AppState.saveProgress();
        
    } catch (error) {
        console.error('開始學習失敗:', error);
        showMessage('開始學習失敗，請重試', 'error');
    }
}

/**
 * 顯示詞彙卡片
 */
function displayVocabularyCard() {
    if (!AppState.vocabularies || AppState.vocabularies.length === 0) {
        showMessage('沒有詞彙數據', 'warning');
        return;
    }
    
    const vocab = AppState.vocabularies[AppState.currentWordIndex];
    if (!vocab) return;
    
    // 更新年級和進度顯示
    if (domElements.gradeDisplay) {
        domElements.gradeDisplay.textContent = vocab.grade;
    }
    if (domElements.progressDisplay) {
        domElements.progressDisplay.textContent = `${AppState.currentWordIndex + 1} / ${AppState.vocabularies.length}`;
    }
    
    // 更新詞彙內容
    if (domElements.cardWord) {
        domElements.cardWord.textContent = vocab.text;
        // 調整字體大小以適應長單詞
        setTimeout(() => adjustFontSize(domElements.cardWord), 10);
    }
    
    if (domElements.cardPhonetic) {
        domElements.cardPhonetic.textContent = vocab.phonetic;
    }
    
    if (domElements.cardTranslationZh) {
        domElements.cardTranslationZh.textContent = vocab.translation_zh_tw;
    }
    
    if (domElements.cardDefinitionEn) {
        domElements.cardDefinitionEn.textContent = vocab.definition_en;
    }
    
    if (domElements.cardSentenceEn) {
        domElements.cardSentenceEn.textContent = vocab.sentence_en;
    }
    
    // 設置圖片
    setVocabularyImage(vocab);
    
    // 更新按鈕狀態
    updateNavigationButtons();
}

/**
 * 設置詞彙圖片
 * @param {Object} vocab - 詞彙對象
 */
function setVocabularyImage(vocab) {
    if (!domElements.cardImageContainer) return;
    
    const img = document.createElement('img');
    img.className = 'w-full h-full object-contain';
    img.alt = vocab.text;
    
    // 優先使用真實圖片，失敗時使用 emoji
    if (vocab.image_path && vocab.image_path !== '') {
        img.src = vocab.image_path;
        img.onerror = function() {
            img.src = generateEmojiSVG(getEmojiForWord(vocab.text));
        };
    } else {
        img.src = generateEmojiSVG(getEmojiForWord(vocab.text));
    }
    
    domElements.cardImageContainer.innerHTML = '';
    domElements.cardImageContainer.appendChild(img);
}

/**
 * 更新導航按鈕狀態
 */
function updateNavigationButtons() {
    if (domElements.prevBtn) {
        domElements.prevBtn.disabled = AppState.currentWordIndex === 0;
    }
    if (domElements.nextBtn) {
        domElements.nextBtn.disabled = AppState.currentWordIndex === AppState.vocabularies.length - 1;
    }
}

/**
 * 上一個詞彙
 */
function previousCard() {
    if (AppState.currentWordIndex > 0) {
        AppState.currentWordIndex--;
        displayVocabularyCard();
        AppState.saveProgress();
    }
}

/**
 * 下一個詞彙
 */
function nextCard() {
    if (AppState.currentWordIndex < AppState.vocabularies.length - 1) {
        AppState.currentWordIndex++;
        displayVocabularyCard();
        AppState.saveProgress();
    }
}

/**
 * 播放當前單詞語音
 */
function speakWord() {
    const vocab = AppState.vocabularies[AppState.currentWordIndex];
    if (vocab && 'speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(vocab.text);
        utterance.lang = 'en-US';
        window.speechSynthesis.speak(utterance);
        console.log('播放語音:', vocab.text);
    } else if (!('speechSynthesis' in window)) {
        console.warn('瀏覽器不支援語音播放功能');
        showMessage('您的瀏覽器不支援語音播放功能', 'warning');
    }
}

/**
 * 開始練習
 */
function startPractice() {
    try {
        console.log('開始練習模式');
        
        AppState.practiceQuestions = generatePracticeQuestions(
            AppState.vocabularies, 
            AppState.allVocabularies
        );
        AppState.currentQuestionIndex = 0;
        AppState.score = 0;
        
        if (AppState.practiceQuestions.length === 0) {
            showMessage('無法生成練習題目', 'warning');
            return;
        }
        
        AppState.switchMode('practice');
        
    } catch (error) {
        console.error('開始練習失敗:', error);
        showMessage('開始練習失敗，請重試', 'error');
    }
}

/**
 * 返回起始畫面
 */
function showStartScreen() {
    AppState.switchMode('start');
}

/**
 * 返回學習模式
 */
function backToStudy() {
    AppState.switchMode('study');
}

/**
 * 顯示練習題目
 */
function displayPracticeQuestion() {
    if (AppState.currentQuestionIndex >= AppState.practiceQuestions.length) {
        showResults();
        return;
    }

    const question = AppState.practiceQuestions[AppState.currentQuestionIndex];

    if (domElements.practiceInstruction) {
        domElements.practiceInstruction.textContent = question.instruction;
    }

    if (domElements.practiceQuestion) {
        domElements.practiceQuestion.innerHTML = question.questionText;
    }

    if (domElements.practiceContent) {
        domElements.practiceContent.innerHTML = '';

        switch (question.type) {
            case 'multiple-choice':
                createMultipleChoiceQuestion(question);
                break;
            case 'typing':
            case 'fill-in-the-blank':
                createTypingQuestion(question);
                break;
        }
    }
}

/**
 * 創建多選題
 * @param {Object} question - 題目對象
 */
function createMultipleChoiceQuestion(question) {
    question.options.forEach(option => {
        const button = document.createElement('button');
        button.className = "w-full bg-white border border-slate-300 hover:bg-slate-100 text-slate-800 font-medium py-3 px-4 rounded-lg";
        button.textContent = option.text;
        button.onclick = () => checkMultipleChoiceAnswer(option.text, question.correctAnswer, button);
        domElements.practiceContent.appendChild(button);
    });
}

/**
 * 創建輸入題
 * @param {Object} question - 題目對象
 */
function createTypingQuestion(question) {
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'w-full border border-slate-300 rounded-lg p-3 text-center text-lg';
    input.placeholder = '在這裡輸入答案';
    input.onkeydown = (event) => {
        if (event.key === 'Enter') {
            checkTypingAnswer(input, question.correctAnswer);
        }
    };
    domElements.practiceContent.appendChild(input);
    input.focus();

    const submitBtn = document.createElement('button');
    submitBtn.textContent = '確定';
    submitBtn.className = 'w-full mt-3 bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 rounded-lg';
    submitBtn.onclick = () => checkTypingAnswer(input, question.correctAnswer);
    domElements.practiceContent.appendChild(submitBtn);
}

/**
 * 檢查多選題答案
 * @param {string} selectedAnswer - 選擇的答案
 * @param {string} correctAnswer - 正確答案
 * @param {HTMLElement} button - 點擊的按鈕
 */
function checkMultipleChoiceAnswer(selectedAnswer, correctAnswer, button) {
    const buttons = domElements.practiceContent.querySelectorAll('button');
    buttons.forEach(btn => btn.disabled = true);

    if (checkAnswer(selectedAnswer, correctAnswer)) {
        AppState.score++;
        button.classList.add('correct-answer');
    } else {
        button.classList.add('wrong-answer');
        // 顯示正確答案
        buttons.forEach(btn => {
            if (checkAnswer(btn.textContent, correctAnswer)) {
                btn.classList.add('correct-answer');
            }
        });
    }

    proceedToNextQuestion();
}

/**
 * 檢查輸入題答案
 * @param {HTMLElement} inputElement - 輸入框元素
 * @param {string} correctAnswer - 正確答案
 */
function checkTypingAnswer(inputElement, correctAnswer) {
    const userAnswer = inputElement.value.trim();
    inputElement.disabled = true;

    const submitBtn = domElements.practiceContent.querySelector('button');
    if (submitBtn) submitBtn.disabled = true;

    if (checkAnswer(userAnswer, correctAnswer)) {
        AppState.score++;
        inputElement.classList.add('correct-answer');
    } else {
        inputElement.classList.add('wrong-answer');
        inputElement.value = `答案: ${correctAnswer}`;
    }

    proceedToNextQuestion();
}

/**
 * 進入下一題
 */
function proceedToNextQuestion() {
    setTimeout(() => {
        AppState.currentQuestionIndex++;
        displayPracticeQuestion();
    }, 1500);
}

/**
 * 顯示練習結果
 */
function showResults() {
    AppState.switchMode('results');
}

/**
 * 顯示結果頁面
 */
function displayResults() {
    const total = AppState.practiceQuestions.length;
    const percentage = calculatePercentage(AppState.score, total);

    if (domElements.scoreDisplay) {
        domElements.scoreDisplay.textContent = `${percentage}%`;
    }

    // 保存成績
    DataService.savePracticeScore(AppState.currentGrade, percentage);

    // 顯示成績訊息
    let message = '';
    if (percentage >= 90) {
        message = '優秀！繼續保持！';
    } else if (percentage >= 70) {
        message = '不錯！再接再厲！';
    } else if (percentage >= 50) {
        message = '還可以，多加練習！';
    } else {
        message = '需要更多練習，加油！';
    }

    setTimeout(() => {
        showMessage(message, 'info', 3000);
    }, 500);
}

// 頁面載入完成後初始化應用
document.addEventListener('DOMContentLoaded', initApp);
